# -*- coding: utf-8 -*-
"""
4c_Test_fborga_2d_3d_optimized_segy.py

Comprehensive test suite for frequency decomposition using the OPTIMIZED Borga transform architecture.
This test validates the integration between file_io_manager.py and fborga_2d_3d_optimized_gmn.py modules,
following the same structure and testing patterns as the reference implementation while adding
performance comparison and validation against the original implementation.

Key Features:
- Tests optimized Borga transform implementation
- Compares performance against original implementation  
- Validates numerical equivalence between original and optimized versions
- Tests different backend options (NumPy, CuPy if available)
- Maintains same modular architecture and test structure
- Uses 'seismic' colormap for visualization

Author: AI Assistant
Based on: 4c_Test_fborga_2d_3d_function_segy.py
"""

import numpy as np
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox, ttk
import time
import os
from tqdm import tqdm
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from scipy import signal as sig

# Import the modular components
import file_io_manager
import fborga_2d_3d_optimized_gmn  # Optimized version for testing
import fborga_2d_3d_gmn  # Original version for comparison
import util  # For backward compatibility with existing workflows

# Check for CuPy availability
try:
    import cupy as cp
    HAS_CUPY = True
    print("CuPy detected - GPU acceleration available")
except ImportError:
    HAS_CUPY = False
    print("CuPy not available - using CPU-only mode")

# --- SYNTHETIC DATA GENERATION FUNCTIONS ---
# (These remain exactly the same as the original test file)

def create_synthetic_2d_data(n_traces=50, n_samples=512, dt=0.002, target_freqs=[20, 35, 55]):
    """
    Create synthetic 2D seismic data with known frequency components for testing.
    
    Parameters
    ----------
    n_traces : int, number of traces
    n_samples : int, number of time samples
    dt : float, sample interval in seconds
    target_freqs : list, target frequencies to embed in the data
    
    Returns
    -------
    seismic_2d : np.ndarray, synthetic 2D seismic data (n_samples, n_traces)
    t : np.ndarray, time axis
    embedded_freqs : list, actual frequencies embedded in the data
    """
    print(f"Creating synthetic 2D data: {n_traces} traces, {n_samples} samples, dt={dt}s")
    
    t = np.arange(0, n_samples * dt, dt)
    seismic_2d = np.zeros((n_samples, n_traces))
    
    # Create different frequency components for each trace
    for i in range(n_traces):
        trace = np.zeros(n_samples)
        
        # Add multiple frequency components with varying amplitudes
        for j, freq in enumerate(target_freqs):
            # Vary amplitude and phase slightly across traces
            amplitude = 1.0 + 0.3 * np.sin(2 * np.pi * i / n_traces)
            phase = 2 * np.pi * i / n_traces
            
            # Create windowed sinusoid
            signal_component = amplitude * np.sin(2 * np.pi * freq * t + phase)
            
            # Apply different windows for different frequencies
            if j == 0:  # Low frequency - full window
                window = sig.windows.tukey(n_samples, 0.2)
            elif j == 1:  # Mid frequency - middle window
                window = np.zeros(n_samples)
                start_idx = n_samples // 4
                end_idx = 3 * n_samples // 4
                window[start_idx:end_idx] = sig.windows.tukey(end_idx - start_idx, 0.3)
            else:  # High frequency - late window
                window = np.zeros(n_samples)
                start_idx = n_samples // 2
                window[start_idx:] = sig.windows.tukey(n_samples - start_idx, 0.4)
            
            trace += signal_component * window
        
        # Add some noise
        noise_level = 0.1
        trace += np.random.randn(n_samples) * noise_level
        
        seismic_2d[:, i] = trace
    
    print(f"Embedded frequencies: {target_freqs} Hz")
    return seismic_2d, t, target_freqs

def create_synthetic_3d_data(n_inlines=20, n_xlines=25, n_samples=256, dt=0.004, target_freqs=[15, 30, 45]):
    """
    Create synthetic 3D seismic data with known frequency components for testing.
    
    Parameters
    ----------
    n_inlines : int, number of inline traces
    n_xlines : int, number of crossline traces  
    n_samples : int, number of time samples
    dt : float, sample interval in seconds
    target_freqs : list, target frequencies to embed in the data
    
    Returns
    -------
    seismic_3d : np.ndarray, synthetic 3D seismic data (n_samples, n_inlines, n_xlines)
    t : np.ndarray, time axis
    embedded_freqs : list, actual frequencies embedded in the data
    """
    print(f"Creating synthetic 3D data: {n_inlines}x{n_xlines} traces, {n_samples} samples, dt={dt}s")
    
    t = np.arange(0, n_samples * dt, dt)
    seismic_3d = np.zeros((n_samples, n_inlines, n_xlines))
    
    # Create spatial variation in frequency content
    for il in range(n_inlines):
        for xl in range(n_xlines):
            trace = np.zeros(n_samples)
            
            # Spatial coordinates for varying properties
            il_norm = il / (n_inlines - 1)
            xl_norm = xl / (n_xlines - 1)
            
            # Add frequency components with spatial variation
            for j, freq in enumerate(target_freqs):
                # Spatial amplitude variation
                spatial_amp = 1.0 + 0.5 * np.sin(2 * np.pi * il_norm) * np.cos(2 * np.pi * xl_norm)
                
                # Temporal window varies with position
                window_start = int(n_samples * (0.1 + 0.3 * il_norm))
                window_end = int(n_samples * (0.6 + 0.3 * xl_norm))
                window_end = min(window_end, n_samples)
                
                if window_end > window_start:
                    signal_component = spatial_amp * np.sin(2 * np.pi * freq * t)
                    window = np.zeros(n_samples)
                    window_length = window_end - window_start
                    window[window_start:window_end] = sig.windows.tukey(window_length, 0.3)
                    
                    trace += signal_component * window
            
            # Add spatially correlated noise
            noise_level = 0.15
            trace += np.random.randn(n_samples) * noise_level
            
            seismic_3d[:, il, xl] = trace
    
    print(f"Embedded frequencies: {target_freqs} Hz")
    return seismic_3d, t, target_freqs

# --- 3D SLICE SELECTION FUNCTIONS ---

def get_3d_slice_selection(seismic_data_shape, geometry='3D'):
    """
    Get user selection for inline or crossline extraction from 3D data.
    
    Parameters
    ----------
    seismic_data_shape : tuple, shape of the 3D seismic data (n_samples, n_inlines, n_xlines)
    geometry : str, data geometry ('3D')
    
    Returns
    -------
    selection_params : dict or None, contains slice_type, slice_index, and selection info
    """
    if geometry != '3D':
        return None
        
    n_samples, n_inlines, n_xlines = seismic_data_shape
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        # Ask user for slice type selection
        slice_choice = messagebox.askyesnocancel(
            "3D Data Slice Selection",
            f"3D Data detected with shape: {seismic_data_shape}\n"
            f"({n_samples} samples, {n_inlines} inlines, {n_xlines} crosslines)\n\n"
            "Choose slice extraction method:\n\n"
            "YES: Extract specific INLINE slice\n"
            "NO: Extract specific CROSSLINE slice\n"
            "CANCEL: Process full 3D volume",
            parent=root
        )
        
        if slice_choice is None:
            # User chose to process full 3D volume
            return {
                'slice_type': 'full_3d',
                'slice_index': None,
                'description': 'Full 3D volume processing'
            }
        
        if slice_choice:  # Inline selection
            slice_type = 'inline'
            max_index = n_inlines - 1
            default_index = n_inlines // 2
            prompt = f"Enter inline index (0 to {max_index}):"
        else:  # Crossline selection
            slice_type = 'crossline'
            max_index = n_xlines - 1
            default_index = n_xlines // 2
            prompt = f"Enter crossline index (0 to {max_index}):"
        
        # Get slice index from user
        slice_index_str = simpledialog.askstring(
            f"Select {slice_type.capitalize()}",
            prompt,
            initialvalue=str(default_index),
            parent=root
        )
        
        if slice_index_str is None:
            return None
            
        try:
            slice_index = int(slice_index_str)
            if slice_index < 0 or slice_index > max_index:
                messagebox.showerror(
                    "Error", 
                    f"Index must be between 0 and {max_index}",
                    parent=root
                )
                return None
                
        except ValueError:
            messagebox.showerror("Error", "Invalid index format", parent=root)
            return None
        
        return {
            'slice_type': slice_type,
            'slice_index': slice_index,
            'description': f'{slice_type.capitalize()} {slice_index} (of {max_index})'
        }
        
    finally:
        root.destroy()

def extract_3d_slice(seismic_data, selection_params):
    """
    Extract 2D slice from 3D seismic data based on user selection.
    
    Parameters
    ----------
    seismic_data : np.ndarray, 3D seismic data (n_samples, n_inlines, n_xlines)
    selection_params : dict, parameters from get_3d_slice_selection()
    
    Returns
    -------
    extracted_data : np.ndarray, 2D slice or full 3D data
    is_3d : bool, True if returning full 3D data, False if 2D slice
    slice_info : dict, information about the extracted slice
    """
    if selection_params is None or selection_params['slice_type'] == 'full_3d':
        return seismic_data, True, {'type': 'full_3d', 'description': 'Full 3D volume'}
    
    slice_type = selection_params['slice_type']
    slice_index = selection_params['slice_index']
    
    if slice_type == 'inline':
        # Extract inline slice: (n_samples, n_xlines)
        extracted_data = seismic_data[:, slice_index, :]
        slice_info = {
            'type': 'inline',
            'index': slice_index,
            'shape': extracted_data.shape,
            'description': f'Inline {slice_index}'
        }
    elif slice_type == 'crossline':
        # Extract crossline slice: (n_samples, n_inlines)
        extracted_data = seismic_data[:, :, slice_index]
        slice_info = {
            'type': 'crossline',
            'index': slice_index,
            'shape': extracted_data.shape,
            'description': f'Crossline {slice_index}'
        }
    else:
        raise ValueError(f"Unknown slice type: {slice_type}")
    
    print(f"Extracted {slice_info['description']} with shape: {slice_info['shape']}")
    return extracted_data, False, slice_info

def apply_optimized_borga_with_slice_selection(seismic_data, target_freqs, fwidth, finc, geometry, backend="auto"):
    """
    Apply optimized Borga transform with optional 3D slice selection.
    
    Parameters
    ----------
    seismic_data : np.ndarray, seismic data (2D or 3D)
    target_freqs : list, target frequencies
    fwidth : float, Gaussian window width
    finc : float, frequency increment
    geometry : str, data geometry ('2D' or '3D')
    backend : str, backend selection ('auto', 'numpy', 'cupy')
    
    Returns
    -------
    results : dict, processing results with slice information
    """
    results = {
        'success': False,
        'error_message': None,
        'slice_selection': None,
        'processing_time': 0,
        'tvs_result': None,
        'fout_selected': None,
        't_out': None,
        'backend_used': backend
    }
    
    try:
        # For 3D data, get user selection
        if geometry == '3D' and seismic_data.ndim == 3:
            selection_params = get_3d_slice_selection(seismic_data.shape, geometry)
            if selection_params is None:
                results['error_message'] = "Slice selection cancelled by user"
                return results
            
            # Extract slice based on selection
            processed_data, is_3d, slice_info = extract_3d_slice(seismic_data, selection_params)
            results['slice_selection'] = slice_info
            
        else:
            # For 2D data, use as-is
            processed_data = seismic_data
            is_3d = False
            results['slice_selection'] = {'type': '2d', 'description': '2D data'}
        
        # Create time axis
        n_samples = processed_data.shape[0]
        dt = 0.004  # Default sample interval
        t = np.arange(n_samples) * dt
        
        # Apply optimized Borga transform
        print(f"Applying optimized Borga transform to {results['slice_selection']['description']}...")
        start_time = time.time()
        
        if is_3d:
            # Process as 3D with optimized version
            tvs_result, fout_selected, t_out = fborga_2d_3d_optimized_gmn.fborga_3d(
                processed_data, t, fwidth, finc, target_freqs=target_freqs, backend=backend
            )
        else:
            # Process as 2D with optimized version
            tvs_result, fout_selected, t_out = fborga_2d_3d_optimized_gmn.fborga_2d(
                processed_data, t, fwidth, finc, target_freqs=target_freqs, backend=backend
            )
        
        processing_time = time.time() - start_time
        
        results.update({
            'success': True,
            'processing_time': processing_time,
            'tvs_result': tvs_result,
            'fout_selected': fout_selected,
            't_out': t_out,
            'seismic_data': processed_data,
            'is_3d': is_3d
        })
        
        print(f"✓ Optimized Borga transform completed in {processing_time:.2f} seconds")
        print(f"✓ Output shape: {tvs_result.shape}")
        print(f"✓ Backend used: {backend}")
        
    except Exception as e:
        results['error_message'] = str(e)
        print(f"✗ Optimized Borga transform failed: {e}")
    
    return results

# --- OPTIMIZED BORGA TEST FUNCTIONS ---

def test_optimized_frequency_decomposition_2d(seismic_data, t, target_freqs, fwidth=8.0, finc=2.0, backend="auto"):
    """
    Test frequency decomposition on 2D data using the optimized Borga module.
    
    Parameters
    ----------
    seismic_data : np.ndarray, 2D seismic data (n_samples, n_traces)
    t : np.ndarray, time axis
    target_freqs : list, target frequencies for decomposition
    fwidth : float, Gaussian window width
    finc : float, frequency increment
    backend : str, backend to use ('auto', 'numpy', 'cupy')
    
    Returns
    -------
    test_results : dict, comprehensive test results and validation metrics
    """
    print(f"\n=== TESTING OPTIMIZED 2D FREQUENCY DECOMPOSITION (Backend: {backend}) ===")
    
    test_results = {
        'success': False,
        'error_message': None,
        'processing_time': 0,
        'output_shape': None,
        'frequency_validation': {},
        'data_validation': {},
        'backend_used': backend
    }
    
    try:
        start_time = time.time()
        
        # Apply optimized Borga transform
        print(f"Applying optimized Borga transform with target frequencies: {target_freqs}")
        tvs_3d, fout_selected, t_out = fborga_2d_3d_optimized_gmn.fborga_2d(
            seismic_data,
            t,
            fwidth=fwidth,
            finc=finc,
            target_freqs=target_freqs,
            backend=backend
        )
        
        processing_time = time.time() - start_time
        test_results['processing_time'] = processing_time
        test_results['output_shape'] = tvs_3d.shape
        
        print(f"Processing completed in {processing_time:.2f} seconds")
        print(f"Output shape: {tvs_3d.shape}")
        print(f"Selected frequencies: {fout_selected}")
        
        # Validation 1: Check output dimensions
        expected_shape = (seismic_data.shape[0], seismic_data.shape[1], len(target_freqs))
        assert tvs_3d.shape == expected_shape, f"Expected shape {expected_shape}, got {tvs_3d.shape}"
        print("✓ Output shape validation passed")
        
        # Validation 2: Check frequency selection accuracy
        freq_errors = []
        for i, target_freq in enumerate(target_freqs):
            actual_freq = fout_selected[i]
            freq_error = abs(actual_freq - target_freq)
            freq_errors.append(freq_error)
            print(f"  Target: {target_freq} Hz, Actual: {actual_freq:.2f} Hz, Error: {freq_error:.2f} Hz")
        
        max_freq_error = max(freq_errors)
        assert max_freq_error < finc, f"Frequency selection error {max_freq_error:.2f} exceeds tolerance {finc}"
        print("✓ Frequency selection validation passed")
        
        test_results['frequency_validation'] = {
            'target_frequencies': target_freqs,
            'actual_frequencies': fout_selected.tolist(),
            'frequency_errors': freq_errors,
            'max_error': max_freq_error
        }
        
        # Validation 3: Check data properties
        assert not np.any(np.isnan(tvs_3d)), "Output contains NaN values"
        assert not np.any(np.isinf(tvs_3d)), "Output contains infinite values"
        print("✓ Data validity validation passed")
        
        # Validation 4: Energy conservation check
        original_energy = np.sum(seismic_data**2)
        reconstructed_energy = np.sum(tvs_3d**2)
        energy_ratio = reconstructed_energy / original_energy
        print(f"Energy ratio (reconstructed/original): {energy_ratio:.3f}")
        
        test_results['data_validation'] = {
            'contains_nan': False,
            'contains_inf': False,
            'original_energy': original_energy,
            'reconstructed_energy': reconstructed_energy,
            'energy_ratio': energy_ratio
        }
        
        test_results['success'] = True
        test_results['tvs_3d'] = tvs_3d
        test_results['fout_selected'] = fout_selected
        test_results['t_out'] = t_out
        
        print("✓ All optimized 2D frequency decomposition tests passed!")
        
    except Exception as e:
        test_results['error_message'] = str(e)
        print(f"✗ Optimized 2D frequency decomposition test failed: {e}")
    
    return test_results

def test_optimized_frequency_decomposition_3d(seismic_data, t, target_freqs, fwidth=8.0, finc=2.0, backend="auto"):
    """
    Test frequency decomposition on 3D data using the optimized Borga module.

    Parameters
    ----------
    seismic_data : np.ndarray, 3D seismic data (n_samples, n_inlines, n_xlines)
    t : np.ndarray, time axis
    target_freqs : list, target frequencies for decomposition
    fwidth : float, Gaussian window width
    finc : float, frequency increment
    backend : str, backend to use ('auto', 'numpy', 'cupy')

    Returns
    -------
    test_results : dict, comprehensive test results and validation metrics
    """
    print(f"\n=== TESTING OPTIMIZED 3D FREQUENCY DECOMPOSITION (Backend: {backend}) ===")

    test_results = {
        'success': False,
        'error_message': None,
        'processing_time': 0,
        'output_shape': None,
        'frequency_validation': {},
        'data_validation': {},
        'backend_used': backend
    }

    try:
        start_time = time.time()

        # Apply optimized Borga transform
        print(f"Applying optimized 3D Borga transform with target frequencies: {target_freqs}")
        tvs_4d, fout_selected, t_out = fborga_2d_3d_optimized_gmn.fborga_3d(
            seismic_data,
            t,
            fwidth=fwidth,
            finc=finc,
            target_freqs=target_freqs,
            backend=backend
        )

        processing_time = time.time() - start_time
        test_results['processing_time'] = processing_time
        test_results['output_shape'] = tvs_4d.shape

        print(f"Processing completed in {processing_time:.2f} seconds")
        print(f"Output shape: {tvs_4d.shape}")
        print(f"Selected frequencies: {fout_selected}")

        # Validation 1: Check output dimensions
        expected_shape = (seismic_data.shape[0], seismic_data.shape[1], seismic_data.shape[2], len(target_freqs))
        assert tvs_4d.shape == expected_shape, f"Expected shape {expected_shape}, got {tvs_4d.shape}"
        print("✓ Output shape validation passed")

        # Validation 2: Check frequency selection accuracy
        freq_errors = []
        for i, target_freq in enumerate(target_freqs):
            actual_freq = fout_selected[i]
            freq_error = abs(actual_freq - target_freq)
            freq_errors.append(freq_error)
            print(f"  Target: {target_freq} Hz, Actual: {actual_freq:.2f} Hz, Error: {freq_error:.2f} Hz")

        max_freq_error = max(freq_errors)
        assert max_freq_error < finc, f"Frequency selection error {max_freq_error:.2f} exceeds tolerance {finc}"
        print("✓ Frequency selection validation passed")

        test_results['frequency_validation'] = {
            'target_frequencies': target_freqs,
            'actual_frequencies': fout_selected.tolist(),
            'frequency_errors': freq_errors,
            'max_error': max_freq_error
        }

        # Validation 3: Check data properties
        assert not np.any(np.isnan(tvs_4d)), "Output contains NaN values"
        assert not np.any(np.isinf(tvs_4d)), "Output contains infinite values"
        print("✓ Data validity validation passed")

        # Validation 4: Energy conservation check
        original_energy = np.sum(seismic_data**2)
        reconstructed_energy = np.sum(tvs_4d**2)
        energy_ratio = reconstructed_energy / original_energy
        print(f"Energy ratio (reconstructed/original): {energy_ratio:.3f}")

        test_results['data_validation'] = {
            'contains_nan': False,
            'contains_inf': False,
            'original_energy': original_energy,
            'reconstructed_energy': reconstructed_energy,
            'energy_ratio': energy_ratio
        }

        test_results['success'] = True
        test_results['tvs_4d'] = tvs_4d
        test_results['fout_selected'] = fout_selected
        test_results['t_out'] = t_out

        print("✓ All optimized 3D frequency decomposition tests passed!")

    except Exception as e:
        test_results['error_message'] = str(e)
        print(f"✗ Optimized 3D frequency decomposition test failed: {e}")

    return test_results

# --- COMPARISON AND VALIDATION FUNCTIONS ---

def compare_original_vs_optimized_2d(seismic_data, t, target_freqs, fwidth=8.0, finc=2.0, tolerance=1e-6):
    """
    Compare original and optimized implementations for 2D data to validate numerical equivalence.

    Parameters
    ----------
    seismic_data : np.ndarray, 2D seismic data (n_samples, n_traces)
    t : np.ndarray, time axis
    target_freqs : list, target frequencies for decomposition
    fwidth : float, Gaussian window width
    finc : float, frequency increment
    tolerance : float, numerical tolerance for equivalence check

    Returns
    -------
    comparison_results : dict, comprehensive comparison results
    """
    print("\n=== COMPARING ORIGINAL VS OPTIMIZED 2D IMPLEMENTATIONS ===")

    comparison_results = {
        'success': False,
        'error_message': None,
        'original_time': 0,
        'optimized_time': 0,
        'speedup_factor': 0,
        'numerical_equivalence': False,
        'max_difference': 0,
        'mean_difference': 0
    }

    try:
        # Test original implementation
        print("Running original implementation...")
        start_time = time.time()
        tvs_original, fout_original, t_original = fborga_2d_3d_gmn.fborga_2d(
            seismic_data, t, fwidth, finc, target_freqs=target_freqs
        )
        original_time = time.time() - start_time
        comparison_results['original_time'] = original_time
        print(f"Original implementation completed in {original_time:.4f} seconds")

        # Test optimized implementation (CPU backend for fair comparison)
        print("Running optimized implementation...")
        start_time = time.time()
        tvs_optimized, fout_optimized, t_optimized = fborga_2d_3d_optimized_gmn.fborga_2d(
            seismic_data, t, fwidth=fwidth, finc=finc, target_freqs=target_freqs, backend="numpy"
        )
        optimized_time = time.time() - start_time
        comparison_results['optimized_time'] = optimized_time
        print(f"Optimized implementation completed in {optimized_time:.4f} seconds")

        # Calculate speedup
        speedup = original_time / optimized_time if optimized_time > 0 else 0
        comparison_results['speedup_factor'] = speedup
        print(f"Speedup factor: {speedup:.2f}x")

        # Check numerical equivalence
        print("Checking numerical equivalence...")

        # Compare shapes
        assert tvs_original.shape == tvs_optimized.shape, f"Shape mismatch: {tvs_original.shape} vs {tvs_optimized.shape}"
        print(f"✓ Output shapes match: {tvs_original.shape}")

        # Compare frequency arrays
        freq_diff = np.abs(fout_original - fout_optimized)
        max_freq_diff = np.max(freq_diff)
        assert max_freq_diff < tolerance, f"Frequency arrays differ by {max_freq_diff:.2e}"
        print(f"✓ Frequency arrays match within tolerance: max diff = {max_freq_diff:.2e}")

        # Compare data arrays
        data_diff = np.abs(tvs_original - tvs_optimized)
        max_diff = np.max(data_diff)
        mean_diff = np.mean(data_diff)

        comparison_results['max_difference'] = max_diff
        comparison_results['mean_difference'] = mean_diff

        numerical_equivalent = max_diff < tolerance
        comparison_results['numerical_equivalence'] = numerical_equivalent

        print(f"Data comparison:")
        print(f"  Max difference: {max_diff:.2e}")
        print(f"  Mean difference: {mean_diff:.2e}")
        print(f"  Tolerance: {tolerance:.2e}")
        print(f"  Numerically equivalent: {'✓' if numerical_equivalent else '✗'}")

        if numerical_equivalent:
            print("✓ Original and optimized implementations produce equivalent results!")
        else:
            print("✗ Warning: Implementations differ beyond tolerance")

        comparison_results['success'] = True
        comparison_results['tvs_original'] = tvs_original
        comparison_results['tvs_optimized'] = tvs_optimized
        comparison_results['fout_original'] = fout_original
        comparison_results['fout_optimized'] = fout_optimized

    except Exception as e:
        comparison_results['error_message'] = str(e)
        print(f"✗ Comparison failed: {e}")

    return comparison_results

def compare_original_vs_optimized_3d(seismic_data, t, target_freqs, fwidth=8.0, finc=2.0, tolerance=1e-6):
    """
    Compare original and optimized implementations for 3D data to validate numerical equivalence.

    Parameters
    ----------
    seismic_data : np.ndarray, 3D seismic data (n_samples, n_inlines, n_xlines)
    t : np.ndarray, time axis
    target_freqs : list, target frequencies for decomposition
    fwidth : float, Gaussian window width
    finc : float, frequency increment
    tolerance : float, numerical tolerance for equivalence check

    Returns
    -------
    comparison_results : dict, comprehensive comparison results
    """
    print("\n=== COMPARING ORIGINAL VS OPTIMIZED 3D IMPLEMENTATIONS ===")

    comparison_results = {
        'success': False,
        'error_message': None,
        'original_time': 0,
        'optimized_time': 0,
        'speedup_factor': 0,
        'numerical_equivalence': False,
        'max_difference': 0,
        'mean_difference': 0
    }

    try:
        # Test original implementation
        print("Running original implementation...")
        start_time = time.time()
        tvs_original, fout_original, t_original = fborga_2d_3d_gmn.fborga_3d(
            seismic_data, t, fwidth, finc, target_freqs=target_freqs
        )
        original_time = time.time() - start_time
        comparison_results['original_time'] = original_time
        print(f"Original implementation completed in {original_time:.4f} seconds")

        # Test optimized implementation (CPU backend for fair comparison)
        print("Running optimized implementation...")
        start_time = time.time()
        tvs_optimized, fout_optimized, t_optimized = fborga_2d_3d_optimized_gmn.fborga_3d(
            seismic_data, t, fwidth=fwidth, finc=finc, target_freqs=target_freqs, backend="numpy"
        )
        optimized_time = time.time() - start_time
        comparison_results['optimized_time'] = optimized_time
        print(f"Optimized implementation completed in {optimized_time:.4f} seconds")

        # Calculate speedup
        speedup = original_time / optimized_time if optimized_time > 0 else 0
        comparison_results['speedup_factor'] = speedup
        print(f"Speedup factor: {speedup:.2f}x")

        # Check numerical equivalence
        print("Checking numerical equivalence...")

        # Compare shapes
        assert tvs_original.shape == tvs_optimized.shape, f"Shape mismatch: {tvs_original.shape} vs {tvs_optimized.shape}"
        print(f"✓ Output shapes match: {tvs_original.shape}")

        # Compare frequency arrays
        freq_diff = np.abs(fout_original - fout_optimized)
        max_freq_diff = np.max(freq_diff)
        assert max_freq_diff < tolerance, f"Frequency arrays differ by {max_freq_diff:.2e}"
        print(f"✓ Frequency arrays match within tolerance: max diff = {max_freq_diff:.2e}")

        # Compare data arrays
        data_diff = np.abs(tvs_original - tvs_optimized)
        max_diff = np.max(data_diff)
        mean_diff = np.mean(data_diff)

        comparison_results['max_difference'] = max_diff
        comparison_results['mean_difference'] = mean_diff

        numerical_equivalent = max_diff < tolerance
        comparison_results['numerical_equivalence'] = numerical_equivalent

        print(f"Data comparison:")
        print(f"  Max difference: {max_diff:.2e}")
        print(f"  Mean difference: {mean_diff:.2e}")
        print(f"  Tolerance: {tolerance:.2e}")
        print(f"  Numerically equivalent: {'✓' if numerical_equivalent else '✗'}")

        if numerical_equivalent:
            print("✓ Original and optimized implementations produce equivalent results!")
        else:
            print("✗ Warning: Implementations differ beyond tolerance")

        comparison_results['success'] = True
        comparison_results['tvs_original'] = tvs_original
        comparison_results['tvs_optimized'] = tvs_optimized
        comparison_results['fout_original'] = fout_original
        comparison_results['fout_optimized'] = fout_optimized

    except Exception as e:
        comparison_results['error_message'] = str(e)
        print(f"✗ Comparison failed: {e}")

    return comparison_results

def test_backend_options(seismic_data, t, target_freqs, fwidth=8.0, finc=2.0):
    """
    Test different backend options for the optimized implementation.

    Parameters
    ----------
    seismic_data : np.ndarray, seismic data (2D or 3D)
    t : np.ndarray, time axis
    target_freqs : list, target frequencies for decomposition
    fwidth : float, Gaussian window width
    finc : float, frequency increment

    Returns
    -------
    backend_results : dict, results for each backend tested
    """
    print("\n=== TESTING DIFFERENT BACKEND OPTIONS ===")

    backend_results = {
        'numpy': {'success': False, 'time': 0, 'error': None},
        'cupy': {'success': False, 'time': 0, 'error': None},
        'auto': {'success': False, 'time': 0, 'error': None}
    }

    backends_to_test = ['numpy', 'auto']
    if HAS_CUPY:
        backends_to_test.append('cupy')

    for backend in backends_to_test:
        print(f"\nTesting backend: {backend}")
        try:
            start_time = time.time()

            if seismic_data.ndim == 2:
                tvs_result, fout_selected, t_out = fborga_2d_3d_optimized_gmn.fborga_2d(
                    seismic_data, t, fwidth=fwidth, finc=finc,
                    target_freqs=target_freqs, backend=backend
                )
            else:  # 3D
                tvs_result, fout_selected, t_out = fborga_2d_3d_optimized_gmn.fborga_3d(
                    seismic_data, t, fwidth=fwidth, finc=finc,
                    target_freqs=target_freqs, backend=backend
                )

            processing_time = time.time() - start_time
            backend_results[backend]['success'] = True
            backend_results[backend]['time'] = processing_time
            backend_results[backend]['output_shape'] = tvs_result.shape

            print(f"✓ Backend {backend} completed in {processing_time:.4f} seconds")

        except Exception as e:
            backend_results[backend]['error'] = str(e)
            print(f"✗ Backend {backend} failed: {e}")

    # Compare performance
    print(f"\nBackend Performance Summary:")
    for backend in backends_to_test:
        result = backend_results[backend]
        if result['success']:
            print(f"  {backend}: {result['time']:.4f}s")
        else:
            print(f"  {backend}: FAILED - {result['error']}")

    return backend_results

def test_optimized_file_io_integration(file_path, target_freqs, fwidth=8.0, finc=2.0, backend="auto"):
    """
    Test the integration between file_io_manager and optimized fborga_2d_3d_optimized_gmn modules.

    Parameters
    ----------
    file_path : str, path to seismic data file
    target_freqs : list, target frequencies for decomposition
    fwidth : float, Gaussian window width
    finc : float, frequency increment
    backend : str, backend to use for optimized implementation

    Returns
    -------
    test_results : dict, comprehensive integration test results
    """
    print(f"\n=== TESTING OPTIMIZED FILE I/O INTEGRATION ===")
    print(f"File: {file_path}")
    print(f"Backend: {backend}")

    test_results = {
        'success': False,
        'error_message': None,
        'file_loading': {},
        'processing_results': {},
        'integration_validation': {},
        'backend_used': backend
    }

    try:
        # Test 1: Load data using file_io_manager
        print("Testing file loading with file_io_manager...")

        # Determine file format
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        if ext in ['.sgy', '.segy']:
            # Try to determine if 2D or 3D
            try:
                seismic_obj = file_io_manager.load_seismic(file_path, 'segy3d')
                data_format = 'segy3d'
                geometry = '3D'
            except:
                seismic_obj = file_io_manager.load_seismic(file_path, 'segy2d')
                data_format = 'segy2d'
                geometry = '2D'
        elif ext == '.npy':
            # Load and check dimensions
            temp_data = np.load(file_path)
            if temp_data.ndim == 3:
                seismic_obj = file_io_manager.load_seismic(file_path, 'numpy3d')
                data_format = 'numpy3d'
                geometry = '3D'
            else:
                seismic_obj = file_io_manager.load_seismic(file_path, 'numpy2d')
                data_format = 'numpy2d'
                geometry = '2D'
        else:
            raise ValueError(f"Unsupported file format: {ext}")

        print(f"✓ Successfully loaded {geometry} data using format: {data_format}")

        test_results['file_loading'] = {
            'format': data_format,
            'geometry': geometry,
            'n_ilines': seismic_obj.get_n_ilines(),
            'n_xlines': seismic_obj.get_n_xlines(),
            'n_zslices': seismic_obj.get_n_zslices()
        }

        # Test 2: Extract data for processing
        if geometry == '2D':
            if hasattr(seismic_obj, 'get_data'):
                seismic_data = seismic_obj.get_data()
            else:
                raise AttributeError("2D seismic object missing get_data() method")

            # Convert to expected format (n_samples, n_traces)
            seismic_data = seismic_data.T

        else:  # 3D
            if hasattr(seismic_obj, 'get_cube'):
                seismic_data = seismic_obj.get_cube()
            elif hasattr(seismic_obj, 'cropped_numpy'):
                # Get full volume
                seismic_data = seismic_obj.cropped_numpy(
                    0, seismic_obj.get_n_ilines(),
                    0, seismic_obj.get_n_xlines(),
                    0, seismic_obj.get_n_zslices()
                )
            else:
                raise AttributeError("3D seismic object missing data access method")

            # Convert to expected format (n_samples, n_inlines, n_xlines)
            seismic_data = np.transpose(seismic_data, (2, 0, 1))

        print(f"✓ Extracted data with shape: {seismic_data.shape}")

        # Test 3: Apply optimized Borga transform with slice selection
        print("Testing optimized Borga transform integration with slice selection...")
        
        # Use the new slice selection functionality
        borga_results = apply_optimized_borga_with_slice_selection(
            seismic_data, target_freqs, fwidth, finc, geometry, backend
        )
        
        if not borga_results['success']:
            raise Exception(f"Borga processing failed: {borga_results['error_message']}")
        
        tvs_result = borga_results['tvs_result']
        fout_selected = borga_results['fout_selected']
        t_out = borga_results['t_out']
        
        # Store slice selection information
        test_results['slice_selection'] = borga_results['slice_selection']

        print(f"✓ Optimized Borga transform completed. Output shape: {tvs_result.shape}")

        test_results['processing_results'] = {
            'output_shape': tvs_result.shape,
            'selected_frequencies': fout_selected.tolist(),
            'time_axis_length': len(t_out)
        }

        # Test 5: Validate integration
        assert tvs_result.shape[0] == seismic_data.shape[0], "Time dimension mismatch"
        assert len(fout_selected) == len(target_freqs), "Frequency count mismatch"

        print("✓ Integration validation passed")

        test_results['integration_validation'] = {
            'time_dimension_match': True,
            'frequency_count_match': True,
            'data_flow_success': True
        }

        test_results['success'] = True
        test_results['seismic_data'] = seismic_data
        test_results['tvs_result'] = tvs_result
        test_results['fout_selected'] = fout_selected
        test_results['t_out'] = t_out

        print("✓ All optimized file I/O integration tests passed!")

    except Exception as e:
        test_results['error_message'] = str(e)
        print(f"✗ Optimized file I/O integration test failed: {e}")

    return test_results

# --- GUI AND USER INTERACTION FUNCTIONS ---

def get_user_inputs_for_optimized_testing():
    """
    Get user inputs for optimized testing parameters using GUI dialogs.

    Returns
    -------
    params : dict or None, user-specified testing parameters
    """
    root = tk.Tk()
    root.withdraw()

    try:
        # Test mode selection
        test_mode = messagebox.askyesnocancel(
            "Optimized Frequency Decomposition Test",
            "Choose test mode:\n\n"
            "YES: Test with real seismic files\n"
            "NO: Run synthetic data tests with comparison\n"
            "CANCEL: Exit",
            parent=root
        )

        if test_mode is None:
            return None

        params = {'test_mode': 'file' if test_mode else 'synthetic'}

        if test_mode:  # File-based testing
            # File selection
            file_path = filedialog.askopenfilename(
                title="Select Seismic Data File",
                filetypes=[
                    ("SEGY files", "*.sgy *.segy"),
                    ("NumPy files", "*.npy"),
                    ("All files", "*.*")
                ],
                parent=root
            )

            if not file_path:
                messagebox.showerror("Error", "File selection cancelled.", parent=root)
                return None

            params['file_path'] = file_path

        # Get target frequencies
        default_freqs = "5, 10, 15, 20, 25, 30"
        freq_str = simpledialog.askstring(
            "Target Frequencies",
            f"Enter target frequencies (Hz, comma-separated):\n(e.g., {default_freqs})",
            initialvalue=default_freqs,
            parent=root
        )

        if freq_str is None:
            return None

        try:
            target_freqs = [float(f.strip()) for f in freq_str.split(',')]
            params['target_frequencies'] = target_freqs
        except ValueError:
            messagebox.showerror("Error", "Invalid frequency format.", parent=root)
            return None

        # Get Borga parameters
        fwidth_str = simpledialog.askstring(
            "Borga Parameters",
            "Enter Gaussian window width (fwidth, Hz):",
            initialvalue="8.0",
            parent=root
        )

        if fwidth_str is None:
            return None

        try:
            params['fwidth'] = float(fwidth_str)
        except ValueError:
            messagebox.showerror("Error", "Invalid fwidth value.", parent=root)
            return None

        finc_str = simpledialog.askstring(
            "Borga Parameters",
            "Enter frequency increment (finc, Hz):",
            initialvalue="2.0",
            parent=root
        )

        if finc_str is None:
            return None

        try:
            params['finc'] = float(finc_str)
        except ValueError:
            messagebox.showerror("Error", "Invalid finc value.", parent=root)
            return None

        # Backend selection
        backend_options = ["auto", "numpy"]
        if HAS_CUPY:
            backend_options.append("cupy")

        backend_choice = messagebox.askyesnocancel(
            "Backend Selection",
            "Choose backend for optimized implementation:\n\n"
            "YES: Auto (automatic selection)\n"
            "NO: NumPy (CPU only)\n" +
            ("CANCEL: CuPy (GPU acceleration)" if HAS_CUPY else "CANCEL: Exit"),
            parent=root
        )

        if backend_choice is None:
            if HAS_CUPY:
                params['backend'] = 'cupy'
            else:
                return None
        elif backend_choice:
            params['backend'] = 'auto'
        else:
            params['backend'] = 'numpy'

        return params

    finally:
        root.destroy()

# --- MAIN WORKFLOW FUNCTIONS ---

def run_optimized_synthetic_tests(target_freqs, fwidth, finc, backend="auto"):
    """
    Run comprehensive tests using synthetic data with the optimized implementation.

    Parameters
    ----------
    target_freqs : list, target frequencies for testing
    fwidth : float, Gaussian window width
    finc : float, frequency increment
    backend : str, backend to use for optimized implementation

    Returns
    -------
    all_results : dict, comprehensive test results
    """
    print("\n" + "="*70)
    print("RUNNING OPTIMIZED SYNTHETIC DATA TESTS WITH COMPARISON")
    print("="*70)

    all_results = {
        'optimized_2d': {},
        'optimized_3d': {},
        'comparison_2d': {},
        'comparison_3d': {},
        'backend_tests': {},
        'overall_success': False
    }

    # Test 1: 2D synthetic data with optimized implementation
    print("\n--- Creating and testing 2D synthetic data (Optimized) ---")
    seismic_2d, t_2d, embedded_freqs_2d = create_synthetic_2d_data(
        n_traces=30, n_samples=256, dt=0.004, target_freqs=target_freqs
    )

    results_2d_opt = test_optimized_frequency_decomposition_2d(
        seismic_2d, t_2d, target_freqs, fwidth, finc, backend
    )
    all_results['optimized_2d'] = results_2d_opt

    # Test 2: Compare 2D original vs optimized
    print("\n--- Comparing 2D original vs optimized implementations ---")
    comparison_2d = compare_original_vs_optimized_2d(
        seismic_2d, t_2d, target_freqs, fwidth, finc
    )
    all_results['comparison_2d'] = comparison_2d

    # Test 3: 3D synthetic data with optimized implementation
    print("\n--- Creating and testing 3D synthetic data (Optimized) ---")
    seismic_3d, t_3d, embedded_freqs_3d = create_synthetic_3d_data(
        n_inlines=15, n_xlines=20, n_samples=128, dt=0.004, target_freqs=target_freqs
    )

    results_3d_opt = test_optimized_frequency_decomposition_3d(
        seismic_3d, t_3d, target_freqs, fwidth, finc, backend
    )
    all_results['optimized_3d'] = results_3d_opt

    # Test 4: Compare 3D original vs optimized
    print("\n--- Comparing 3D original vs optimized implementations ---")
    comparison_3d = compare_original_vs_optimized_3d(
        seismic_3d, t_3d, target_freqs, fwidth, finc
    )
    all_results['comparison_3d'] = comparison_3d

    # Test 5: Backend options testing
    print("\n--- Testing different backend options ---")
    backend_results = test_backend_options(seismic_2d, t_2d, target_freqs, fwidth, finc)
    all_results['backend_tests'] = backend_results

    # Overall assessment
    all_results['overall_success'] = (
        results_2d_opt['success'] and results_3d_opt['success'] and
        comparison_2d['success'] and comparison_3d['success']
    )

    print("\n" + "="*70)
    print("OPTIMIZED SYNTHETIC TESTS SUMMARY")
    print("="*70)
    print(f"2D Optimized Test: {'PASSED' if results_2d_opt['success'] else 'FAILED'}")
    print(f"3D Optimized Test: {'PASSED' if results_3d_opt['success'] else 'FAILED'}")
    print(f"2D Comparison: {'PASSED' if comparison_2d['success'] else 'FAILED'}")
    print(f"3D Comparison: {'PASSED' if comparison_3d['success'] else 'FAILED'}")

    if comparison_2d['success']:
        print(f"2D Speedup: {comparison_2d['speedup_factor']:.2f}x")
        print(f"2D Numerical Equivalence: {'✓' if comparison_2d['numerical_equivalence'] else '✗'}")

    if comparison_3d['success']:
        print(f"3D Speedup: {comparison_3d['speedup_factor']:.2f}x")
        print(f"3D Numerical Equivalence: {'✓' if comparison_3d['numerical_equivalence'] else '✗'}")

    print(f"Overall: {'ALL TESTS PASSED' if all_results['overall_success'] else 'SOME TESTS FAILED'}")

    return all_results

def run_optimized_file_based_tests(file_path, target_freqs, fwidth, finc, backend="auto"):
    """
    Run tests using real seismic data files with the optimized implementation.

    Parameters
    ----------
    file_path : str, path to seismic data file
    target_freqs : list, target frequencies for testing
    fwidth : float, Gaussian window width
    finc : float, frequency increment
    backend : str, backend to use for optimized implementation

    Returns
    -------
    test_results : dict, comprehensive test results
    """
    print("\n" + "="*70)
    print("RUNNING OPTIMIZED FILE-BASED TESTS")
    print("="*70)
    print(f"File: {file_path}")
    print(f"Backend: {backend}")

    # Test optimized file I/O integration
    results = test_optimized_file_io_integration(file_path, target_freqs, fwidth, finc, backend)

    print("\n" + "="*70)
    print("OPTIMIZED FILE-BASED TESTS SUMMARY")
    print("="*70)
    print(f"Integration Test: {'PASSED' if results['success'] else 'FAILED'}")

    if not results['success']:
        print(f"Error: {results['error_message']}")

    return results

# --- VISUALIZATION FUNCTIONS ---
# (Reusing the same visualization functions from the original test with seismic colormap)

def plot_optimized_frequency_decomposition_results(parent_frame, results, title):
    """
    Plot optimized frequency decomposition results in the given frame.

    Parameters
    ----------
    parent_frame : tk.Frame, parent frame for the plot
    results : dict, test results containing decomposition data
    title : str, title for the plots
    """
    # Create matplotlib figure
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle(f'Optimized Frequency Decomposition Results: {title}', fontsize=16)

    # Get data
    if 'tvs_3d' in results:  # 2D data results
        tvs_data = results['tvs_3d']
        original_data = None  # We don't store original in results
        is_3d = False
    elif 'tvs_4d' in results:  # 3D data results
        tvs_data = results['tvs_4d']
        original_data = None
        is_3d = True
    elif 'tvs_result' in results:  # File-based results
        tvs_data = results['tvs_result']
        original_data = results.get('seismic_data', None)
        is_3d = len(tvs_data.shape) == 4
    else:
        # No data to plot
        axes[0, 0].text(0.5, 0.5, 'No decomposition data available',
                       ha='center', va='center', transform=axes[0, 0].transAxes)
        return

    fout_selected = results.get('fout_selected', [])

    # Plot original seismic data in subplot (0,0) as reference
    ax_orig = axes[0, 0]
    if original_data is not None:
        if is_3d:
            # For 3D data, show middle inline slice of original data
            mid_il = original_data.shape[1] // 2 if original_data.ndim == 3 else 0
            if original_data.ndim == 3:
                orig_slice = original_data[:, mid_il, :]  # (time, xline)
            else:
                orig_slice = original_data  # Already 2D

            # Calculate percentile-based scaling for better visualization
            vmin_orig, vmax_orig = np.percentile(orig_slice, [2, 98])

            # Plot with correct orientation: traces on x-axis, time on y-axis
            im_orig = ax_orig.imshow(orig_slice, aspect='auto', cmap='seismic',
                                   vmin=vmin_orig, vmax=vmax_orig,
                                   extent=[0, orig_slice.shape[1], orig_slice.shape[0], 0])
            ax_orig.set_title(f'Original Seismic (IL: {mid_il})' if is_3d else 'Original Seismic (2D)')
            ax_orig.set_xlabel('Crosslines' if is_3d else 'Traces')
            ax_orig.set_ylabel('Time Samples')
            plt.colorbar(im_orig, ax=ax_orig, shrink=0.8, label='Amplitude')
        else:
            # For 2D data
            # Calculate percentile-based scaling
            vmin_orig, vmax_orig = np.percentile(original_data, [2, 98])

            # Plot with correct orientation: traces on x-axis, time on y-axis
            im_orig = ax_orig.imshow(original_data, aspect='auto', cmap='seismic',
                                   vmin=vmin_orig, vmax=vmax_orig,
                                   extent=[0, original_data.shape[1], original_data.shape[0], 0])
            ax_orig.set_title('Original Seismic (2D)')
            ax_orig.set_xlabel('Traces')
            ax_orig.set_ylabel('Time Samples')
            plt.colorbar(im_orig, ax=ax_orig, shrink=0.8, label='Amplitude')
    else:
        # No original data available
        ax_orig.text(0.5, 0.5, 'Original seismic data\nnot available',
                    ha='center', va='center', transform=ax_orig.transAxes,
                    fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        ax_orig.set_title('Original Seismic')

    # Plot frequency slices (start from position 1 since 0,0 is for original)
    n_freqs = min(len(fout_selected), 5)  # Show up to 5 frequency slices (6 total - 1 for original)

    plot_positions = [(0, 1), (0, 2), (1, 0), (1, 1), (1, 2)]  # Skip (0,0) for original

    for i in range(n_freqs):
        row, col = plot_positions[i]
        ax = axes[row, col]

        if is_3d:
            # For 3D data, show middle inline slice
            mid_il = tvs_data.shape[1] // 2
            freq_slice = tvs_data[:, mid_il, :, i]  # (time, xline)
        else:
            # For 2D data
            freq_slice = tvs_data[:, :, i]  # (time, trace)

        # Calculate percentile-based scaling for better visualization
        vmin_voice, vmax_voice = np.percentile(freq_slice, [2, 98])

        # Plot with correct orientation: traces/crosslines on x-axis, time on y-axis
        im = ax.imshow(freq_slice, aspect='auto', cmap='seismic',
                      vmin=vmin_voice, vmax=vmax_voice,
                      extent=[0, freq_slice.shape[1], freq_slice.shape[0], 0])
        ax.set_title(f'Spectral Voice (Optimized)\n{fout_selected[i]:.1f} Hz')
        ax.set_xlabel('Traces' if not is_3d else 'Crosslines')
        ax.set_ylabel('Time Samples')

        # Add colorbar
        plt.colorbar(im, ax=ax, shrink=0.8, label='Amplitude')

    # Hide unused subplots
    for i in range(n_freqs, 5):
        row, col = plot_positions[i]
        axes[row, col].set_visible(False)

    plt.tight_layout()

    # Embed in tkinter
    canvas = FigureCanvasTkAgg(fig, master=parent_frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

def display_optimized_test_results(test_results, test_mode):
    """
    Display comprehensive optimized test results in a GUI window.

    Parameters
    ----------
    test_results : dict, test results to display
    test_mode : str, 'synthetic' or 'file'
    """
    # Create main window
    result_window = tk.Tk()
    result_window.title("Optimized Frequency Decomposition Test Results")
    result_window.geometry("1200x800")

    # Create notebook for tabs
    notebook = ttk.Notebook(result_window)
    notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    if test_mode == 'synthetic':
        # Tab 1: 2D Optimized Results
        if 'optimized_2d' in test_results and test_results['optimized_2d']['success']:
            frame_2d = ttk.Frame(notebook)
            notebook.add(frame_2d, text="2D Optimized Results")

            results_2d = test_results['optimized_2d']
            plot_optimized_frequency_decomposition_results(
                frame_2d, results_2d, "2D Synthetic Data (Optimized)"
            )

        # Tab 2: 3D Optimized Results
        if 'optimized_3d' in test_results and test_results['optimized_3d']['success']:
            frame_3d = ttk.Frame(notebook)
            notebook.add(frame_3d, text="3D Optimized Results")

            results_3d = test_results['optimized_3d']
            plot_optimized_frequency_decomposition_results(
                frame_3d, results_3d, "3D Synthetic Data (Optimized)"
            )

        # Tab 3: Performance Comparison
        if 'comparison_2d' in test_results or 'comparison_3d' in test_results:
            frame_comparison = ttk.Frame(notebook)
            notebook.add(frame_comparison, text="Performance Comparison")

            # Create comparison visualization
            create_performance_comparison_plot(frame_comparison, test_results)

    else:  # file mode
        if test_results['success']:
            frame_file = ttk.Frame(notebook)
            notebook.add(frame_file, text="Optimized File-Based Results")

            plot_optimized_frequency_decomposition_results(
                frame_file, test_results, "Real Seismic Data (Optimized)"
            )

    # Tab: Summary
    frame_summary = ttk.Frame(notebook)
    notebook.add(frame_summary, text="Test Summary")

    # Create summary text
    summary_text = tk.Text(frame_summary, wrap=tk.WORD, font=("Courier", 10))
    summary_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Generate summary content
    summary_content = generate_optimized_test_summary(test_results, test_mode)
    summary_text.insert(tk.END, summary_content)
    summary_text.config(state=tk.DISABLED)

    # Add scrollbar
    scrollbar = ttk.Scrollbar(frame_summary, orient=tk.VERTICAL, command=summary_text.yview)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    summary_text.config(yscrollcommand=scrollbar.set)

    # Close button
    close_button = ttk.Button(result_window, text="Close", command=result_window.destroy)
    close_button.pack(pady=10)

    # Center window
    result_window.update_idletasks()
    x = (result_window.winfo_screenwidth() // 2) - (1200 // 2)
    y = (result_window.winfo_screenheight() // 2) - (800 // 2)
    result_window.geometry(f"1200x800+{x}+{y}")

    result_window.mainloop()

def create_performance_comparison_plot(parent_frame, test_results):
    """
    Create a performance comparison plot showing speedup and accuracy.
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle('Performance Comparison: Original vs Optimized', fontsize=14)

    # Plot 1: Processing Time Comparison
    categories = []
    original_times = []
    optimized_times = []

    if 'comparison_2d' in test_results and test_results['comparison_2d']['success']:
        categories.append('2D Test')
        original_times.append(test_results['comparison_2d']['original_time'])
        optimized_times.append(test_results['comparison_2d']['optimized_time'])

    if 'comparison_3d' in test_results and test_results['comparison_3d']['success']:
        categories.append('3D Test')
        original_times.append(test_results['comparison_3d']['original_time'])
        optimized_times.append(test_results['comparison_3d']['optimized_time'])

    if categories:
        x = np.arange(len(categories))
        width = 0.35

        ax1.bar(x - width/2, original_times, width, label='Original', alpha=0.8)
        ax1.bar(x + width/2, optimized_times, width, label='Optimized', alpha=0.8)
        ax1.set_xlabel('Test Type')
        ax1.set_ylabel('Processing Time (seconds)')
        ax1.set_title('Processing Time Comparison')
        ax1.set_xticks(x)
        ax1.set_xticklabels(categories)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

    # Plot 2: Speedup Factor
    speedups = []
    for cat in categories:
        if '2D' in cat and 'comparison_2d' in test_results:
            speedups.append(test_results['comparison_2d']['speedup_factor'])
        elif '3D' in cat and 'comparison_3d' in test_results:
            speedups.append(test_results['comparison_3d']['speedup_factor'])

    if speedups:
        bars = ax2.bar(categories, speedups, alpha=0.8, color='green')
        ax2.set_xlabel('Test Type')
        ax2.set_ylabel('Speedup Factor (x)')
        ax2.set_title('Performance Improvement')
        ax2.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar, speedup in zip(bars, speedups):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    f'{speedup:.2f}x', ha='center', va='bottom')

    plt.tight_layout()

    # Embed in tkinter
    canvas = FigureCanvasTkAgg(fig, master=parent_frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

def generate_optimized_test_summary(test_results, test_mode):
    """
    Generate a comprehensive text summary of optimized test results.

    Parameters
    ----------
    test_results : dict, test results
    test_mode : str, 'synthetic' or 'file'

    Returns
    -------
    summary : str, formatted summary text
    """
    summary = []
    summary.append("OPTIMIZED FREQUENCY DECOMPOSITION TEST SUMMARY")
    summary.append("=" * 60)
    summary.append(f"Test Mode: {test_mode.upper()}")
    summary.append("")

    if test_mode == 'synthetic':
        # Optimized synthetic test summary
        summary.append("OPTIMIZED SYNTHETIC DATA TESTS:")
        summary.append("-" * 35)

        for test_type in ['optimized_2d', 'optimized_3d']:
            if test_type in test_results:
                results = test_results[test_type]
                test_name = "2D Optimized Test" if "2d" in test_type else "3D Optimized Test"

                summary.append(f"\n{test_name}:")
                summary.append(f"  Status: {'PASSED' if results['success'] else 'FAILED'}")

                if results['success']:
                    summary.append(f"  Backend Used: {results['backend_used']}")
                    summary.append(f"  Processing Time: {results['processing_time']:.4f} seconds")
                    summary.append(f"  Output Shape: {results['output_shape']}")

                    freq_val = results['frequency_validation']
                    summary.append(f"  Target Frequencies: {freq_val['target_frequencies']}")
                    summary.append(f"  Actual Frequencies: {[f'{f:.2f}' for f in freq_val['actual_frequencies']]}")
                    summary.append(f"  Max Frequency Error: {freq_val['max_error']:.3f} Hz")

                    data_val = results['data_validation']
                    summary.append(f"  Energy Ratio: {data_val['energy_ratio']:.3f}")
                else:
                    summary.append(f"  Error: {results['error_message']}")

        # Performance comparison summary
        summary.append(f"\nPERFORMANCE COMPARISON:")
        summary.append("-" * 25)

        for comp_type in ['comparison_2d', 'comparison_3d']:
            if comp_type in test_results and test_results[comp_type]['success']:
                comp_results = test_results[comp_type]
                comp_name = "2D Comparison" if "2d" in comp_type else "3D Comparison"

                summary.append(f"\n{comp_name}:")
                summary.append(f"  Original Time: {comp_results['original_time']:.4f} seconds")
                summary.append(f"  Optimized Time: {comp_results['optimized_time']:.4f} seconds")
                summary.append(f"  Speedup Factor: {comp_results['speedup_factor']:.2f}x")
                summary.append(f"  Numerical Equivalence: {'✓' if comp_results['numerical_equivalence'] else '✗'}")
                summary.append(f"  Max Difference: {comp_results['max_difference']:.2e}")
                summary.append(f"  Mean Difference: {comp_results['mean_difference']:.2e}")

        # Backend testing summary
        if 'backend_tests' in test_results:
            summary.append(f"\nBACKEND TESTING:")
            summary.append("-" * 20)
            backend_results = test_results['backend_tests']

            for backend, result in backend_results.items():
                if result['success']:
                    summary.append(f"  {backend.upper()}: {result['time']:.4f}s")
                else:
                    summary.append(f"  {backend.upper()}: FAILED - {result['error']}")

        summary.append(f"\nOverall Result: {'ALL TESTS PASSED' if test_results.get('overall_success', False) else 'SOME TESTS FAILED'}")

    else:  # file mode
        summary.append("OPTIMIZED FILE-BASED INTEGRATION TEST:")
        summary.append("-" * 40)

        summary.append(f"\nStatus: {'PASSED' if test_results['success'] else 'FAILED'}")

        if test_results['success']:
            file_loading = test_results['file_loading']
            summary.append(f"File Format: {file_loading['format']}")
            summary.append(f"Geometry: {file_loading['geometry']}")
            summary.append(f"Backend Used: {test_results['backend_used']}")
            summary.append(f"Dimensions: {file_loading['n_ilines']} x {file_loading['n_xlines']} x {file_loading['n_zslices']}")

            proc_results = test_results['processing_results']
            summary.append(f"Output Shape: {proc_results['output_shape']}")
            summary.append(f"Selected Frequencies: {[f'{f:.2f}' for f in proc_results['selected_frequencies']]}")

            integration = test_results['integration_validation']
            summary.append(f"Time Dimension Match: {integration['time_dimension_match']}")
            summary.append(f"Frequency Count Match: {integration['frequency_count_match']}")
            summary.append(f"Data Flow Success: {integration['data_flow_success']}")
        else:
            summary.append(f"Error: {test_results['error_message']}")

    summary.append("\n" + "=" * 60)
    summary.append("Optimized test completed successfully!")
    summary.append("Key Benefits:")
    summary.append("- Improved performance with vectorized operations")
    summary.append("- GPU acceleration support (if CuPy available)")
    summary.append("- Numerical equivalence with original implementation")
    summary.append("- Flexible backend selection (auto/numpy/cupy)")

    return "\n".join(summary)

def main():
    """
    Main function to run the optimized frequency decomposition tests.
    """
    print("OPTIMIZED FREQUENCY DECOMPOSITION TEST SUITE")
    print("Using modular architecture: file_io_manager.py + fborga_2d_3d_optimized_gmn.py")
    print("=" * 80)

    # Get user inputs
    params = get_user_inputs_for_optimized_testing()
    if params is None:
        print("Test cancelled by user.")
        return

    # Extract parameters
    test_mode = params['test_mode']
    target_freqs = params['target_frequencies']
    fwidth = params['fwidth']
    finc = params['finc']
    backend = params['backend']

    print(f"\nTest Parameters:")
    print(f"  Mode: {test_mode}")
    print(f"  Target Frequencies: {target_freqs}")
    print(f"  Gaussian Width: {fwidth}")
    print(f"  Frequency Increment: {finc}")
    print(f"  Backend: {backend}")

    # Run tests based on mode
    if test_mode == 'synthetic':
        test_results = run_optimized_synthetic_tests(target_freqs, fwidth, finc, backend)
    else:  # file mode
        file_path = params['file_path']
        test_results = run_optimized_file_based_tests(file_path, target_freqs, fwidth, finc, backend)

    # Display results
    display_optimized_test_results(test_results, test_mode)

if __name__ == '__main__':
    main()
